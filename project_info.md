# Overview
We are working on a new scientific manuscript. Idea we are using different ML methods (RF, SVM, XGBoost, and ANN) to predict the Ksat of the soil using other soil properties, LULC, NDVI and other related variables. We have few hundread sites. We will train the model on these sites, then we will predict the Ksat for the whole study area which is CONUS.

# Problem
While training RF I am facing overfitting. 

# What I need:
Using python, write a Random Forest code (as .ipynb) that can handle overfitting. Show complete example. from training to prediction.

There are 5770 ovservations we have.  Its there in data.xlsx file. First column Ksat is the target variable. Header variable is there. Other headers are CEC, LU, NDVI, Pr,	Slope,	TWI, T,	Texture, db, depth,	soil_acidity, clay(%),	sand(%), silt(%), soc(%), PWP and FC.

Here LU numeric categorical variables, and Texture is string categorical variables. Rest are numeric continuous. That's why these two need hot encoding. Need to optimizee the hyperparameters of the RF using grid search. While grid search, for reducing the overfitting, use 10 fold cross validation. Need to normalize the data before training. Use only important parameters for training.

We also need to find the most important variables that will be used in the model training.

