import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, GridSearchCV, cross_val_score, KFold
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import <PERSON><PERSON>ncoder, StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.inspection import permutation_importance
import warnings
warnings.filterwarnings('ignore')

# Set random seed for reproducibility
np.random.seed(42)

print("Libraries imported successfully!")

# Load the data
data = pd.read_excel('data.xlsx')

print(f"Dataset shape: {data.shape}")
print(f"\nColumn names:")
print(data.columns.tolist())
print(f"\nFirst few rows:")
data.head()

# Basic data information
print("Data Info:")
print(data.info())
print("\nBasic Statistics:")
data.describe()

# Check for missing values
print("Missing values per column:")
missing_values = data.isnull().sum()
print(missing_values[missing_values > 0])

if missing_values.sum() == 0:
    print("No missing values found!")

# Separate target variable and features
target = 'Ksat'
y = data[target]
X = data.drop(columns=[target])

print(f"Target variable: {target}")
print(f"Features: {X.columns.tolist()}")
print(f"\nTarget variable statistics:")
print(y.describe())

# Identify categorical variables
categorical_vars = ['LU', 'Texture']  # As specified in the problem
numerical_vars = [col for col in X.columns if col not in categorical_vars]

print(f"Categorical variables: {categorical_vars}")
print(f"Numerical variables: {numerical_vars}")

# Check unique values in categorical variables
for var in categorical_vars:
    print(f"\n{var} unique values: {X[var].nunique()}")
    print(f"Values: {X[var].unique()[:10]}...")  # Show first 10 values

# One-hot encoding for categorical variables
X_encoded = X.copy()

# One-hot encode LU (numeric categorical)
lu_dummies = pd.get_dummies(X_encoded['LU'], prefix='LU')
X_encoded = pd.concat([X_encoded, lu_dummies], axis=1)
X_encoded.drop('LU', axis=1, inplace=True)

# One-hot encode Texture (string categorical)
texture_dummies = pd.get_dummies(X_encoded['Texture'], prefix='Texture')
X_encoded = pd.concat([X_encoded, texture_dummies], axis=1)
X_encoded.drop('Texture', axis=1, inplace=True)

print(f"Shape after encoding: {X_encoded.shape}")
print(f"New columns added: {X_encoded.shape[1] - len(numerical_vars)}")
print(f"\nFinal feature names:")
print(X_encoded.columns.tolist())

# Distribution of target variable
plt.figure(figsize=(12, 4))

plt.subplot(1, 2, 1)
plt.hist(y, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
plt.title('Distribution of Ksat')
plt.xlabel('Ksat')
plt.ylabel('Frequency')

plt.subplot(1, 2, 2)
plt.boxplot(y)
plt.title('Boxplot of Ksat')
plt.ylabel('Ksat')

plt.tight_layout()
plt.show()

print(f"Ksat statistics:")
print(f"Mean: {y.mean():.4f}")
print(f"Median: {y.median():.4f}")
print(f"Std: {y.std():.4f}")
print(f"Skewness: {y.skew():.4f}")

# Correlation matrix for numerical variables
numerical_data = data[numerical_vars + [target]]
correlation_matrix = numerical_data.corr()

plt.figure(figsize=(12, 10))
sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, 
            square=True, fmt='.2f', cbar_kws={'shrink': 0.8})
plt.title('Correlation Matrix of Numerical Variables')
plt.tight_layout()
plt.show()

# Show correlations with target variable
target_correlations = correlation_matrix[target].abs().sort_values(ascending=False)
print("\nCorrelations with Ksat (absolute values):")
print(target_correlations[1:])  # Exclude self-correlation

# Split the data
X_train, X_test, y_train, y_test = train_test_split(
    X_encoded, y, test_size=0.2, random_state=42, stratify=None
)

print(f"Training set size: {X_train.shape[0]} samples")
print(f"Test set size: {X_test.shape[0]} samples")
print(f"Number of features: {X_train.shape[1]}")

# Verify the split
print(f"\nTraining set target statistics:")
print(f"Mean: {y_train.mean():.4f}, Std: {y_train.std():.4f}")
print(f"\nTest set target statistics:")
print(f"Mean: {y_test.mean():.4f}, Std: {y_test.std():.4f}")

# Create baseline Random Forest model
rf_baseline = RandomForestRegressor(random_state=42, n_jobs=-1)

# Fit the model
rf_baseline.fit(X_train, y_train)

# Make predictions
y_train_pred_baseline = rf_baseline.predict(X_train)
y_test_pred_baseline = rf_baseline.predict(X_test)

# Calculate metrics
train_r2_baseline = r2_score(y_train, y_train_pred_baseline)
test_r2_baseline = r2_score(y_test, y_test_pred_baseline)
train_rmse_baseline = np.sqrt(mean_squared_error(y_train, y_train_pred_baseline))
test_rmse_baseline = np.sqrt(mean_squared_error(y_test, y_test_pred_baseline))

print("Baseline Random Forest Results:")
print(f"Training R²: {train_r2_baseline:.4f}")
print(f"Test R²: {test_r2_baseline:.4f}")
print(f"Training RMSE: {train_rmse_baseline:.4f}")
print(f"Test RMSE: {test_rmse_baseline:.4f}")
print(f"\nOverfitting indicator (Train R² - Test R²): {train_r2_baseline - test_r2_baseline:.4f}")

if train_r2_baseline - test_r2_baseline > 0.1:
    print("⚠️ Potential overfitting detected! Need hyperparameter tuning.")
else:
    print("✅ No significant overfitting detected.")